<?php
/**
 * About Section API Handler
 */

function handleAboutAPI($method, $id) {
    switch ($method) {
        case 'GET':
            getAboutSection();
            break;

        case 'PUT':
            updateAboutSection();
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAboutSection() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM about_section LIMIT 1");
        $about = $stmt->fetch();

        if ($about) {
            sendResponse(['success' => true, 'data' => $about]);
        } else {
            sendResponse(['error' => 'About section not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch about section: ' . $e->getMessage()], 500);
    }
}

function updateAboutSection() {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = [
            'main_heading', 'main_description', 'background_image',
            'service_1', 'service_2', 'service_3', 'service_4', 'service_5', 'service_6',
            'detailed_description'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        // Check if record exists
        $checkStmt = $pdo->query("SELECT COUNT(*) as count FROM about_section");
        $count = $checkStmt->fetch()['count'];

        if ($count == 0) {
            // Insert new record
            $sql = "INSERT INTO about_section (" . implode(', ', array_keys($data)) . ") VALUES (" . str_repeat('?,', count($data) - 1) . "?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute(array_values($data));
            sendResponse(['success' => true, 'message' => 'About section created successfully']);
        } else {
            // Update existing record
            $sql = "UPDATE about_section SET " . implode(', ', $fields) . " LIMIT 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
            sendResponse(['success' => true, 'message' => 'About section updated successfully']);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update about section: ' . $e->getMessage()], 500);
    }
}