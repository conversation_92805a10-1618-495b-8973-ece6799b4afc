<?php
/**
 * Contact Details API Handler
 */

function handleContactAPI($method, $id) {
    switch ($method) {
        case 'GET':
            getContactDetails();
            break;

        case 'PUT':
            updateContactDetails();
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getContactDetails() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM contact_details LIMIT 1");
        $contact = $stmt->fetch();

        if ($contact) {
            sendResponse(['success' => true, 'data' => $contact]);
        } else {
            sendResponse(['error' => 'Contact details not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch contact details: ' . $e->getMessage()], 500);
    }
}

function updateContactDetails() {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = [
            'phone', 'email', 'address', 'whatsapp_number',
            'facebook_url', 'twitter_url', 'instagram_url',
            'company_name', 'company_tagline'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        // Check if record exists
        $checkStmt = $pdo->query("SELECT COUNT(*) as count FROM contact_details");
        $count = $checkStmt->fetch()['count'];

        if ($count == 0) {
            // Insert new record
            $sql = "INSERT INTO contact_details (" . implode(', ', array_keys($data)) . ") VALUES (" . str_repeat('?,', count($data) - 1) . "?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute(array_values($data));
            sendResponse(['success' => true, 'message' => 'Contact details created successfully']);
        } else {
            // Update existing record
            $sql = "UPDATE contact_details SET " . implode(', ', $fields) . " LIMIT 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
            sendResponse(['success' => true, 'message' => 'Contact details updated successfully']);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update contact details: ' . $e->getMessage()], 500);
    }
}