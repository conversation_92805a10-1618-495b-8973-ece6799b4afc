<?php
/**
 * CTA Sections API Handler
 */

function handleCTAAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getCTASection($id);
            } else {
                getAllCTASections();
            }
            break;

        case 'PUT':
            if ($id) {
                updateCTASection($id);
            } else {
                sendResponse(['error' => 'Section type required for update'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllCTASections() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM cta_sections ORDER BY section_type");
        $sections = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $sections]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch CTA sections: ' . $e->getMessage()], 500);
    }
}

function getCTASection($sectionType) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM cta_sections WHERE section_type = ?");
        $stmt->execute([$sectionType]);
        $section = $stmt->fetch();

        if ($section) {
            sendResponse(['success' => true, 'data' => $section]);
        } else {
            sendResponse(['error' => 'CTA section not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch CTA section: ' . $e->getMessage()], 500);
    }
}

function updateCTASection($sectionType) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = [
            'heading', 'background_image',
            'counter_1_number', 'counter_1_label',
            'counter_2_number', 'counter_2_label',
            'counter_3_number', 'counter_3_label',
            'counter_4_number', 'counter_4_label'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $sectionType;
        $sql = "UPDATE cta_sections SET " . implode(', ', $fields) . " WHERE section_type = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'CTA section updated successfully']);
        } else {
            sendResponse(['error' => 'CTA section not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update CTA section: ' . $e->getMessage()], 500);
    }
}