<?php
/**
 * Hotels API Handler
 */

function handleHotelsAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getHotel($id);
            } else {
                getAllHotels();
            }
            break;

        case 'POST':
            createHotel();
            break;

        case 'PUT':
            if ($id) {
                updateHotel($id);
            } else {
                sendResponse(['error' => 'ID required for update'], 400);
            }
            break;

        case 'DELETE':
            if ($id) {
                deleteHotel($id);
            } else {
                sendResponse(['error' => 'ID required for delete'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllHotels() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM hotels WHERE is_active = 1 ORDER BY sort_order ASC");
        $hotels = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $hotels]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch hotels: ' . $e->getMessage()], 500);
    }
}

function getHotel($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM hotels WHERE id = ?");
        $stmt->execute([$id]);
        $hotel = $stmt->fetch();

        if ($hotel) {
            sendResponse(['success' => true, 'data' => $hotel]);
        } else {
            sendResponse(['error' => 'Hotel not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch hotel: ' . $e->getMessage()], 500);
    }
}

function createHotel() {
    try {
        $data = getJSONInput();

        if (!$data || !isset($data['title'], $data['category'], $data['image'])) {
            sendResponse(['error' => 'Missing required fields'], 400);
        }

        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO hotels (title, category, image, link, sort_order, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['title'],
            $data['category'],
            $data['image'],
            $data['link'] ?? 'https://wa.me/919653737456',
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? true
        ]);

        $id = $pdo->lastInsertId();
        sendResponse(['success' => true, 'message' => 'Hotel created successfully', 'id' => $id]);

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to create hotel: ' . $e->getMessage()], 500);
    }
}

function updateHotel($id) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = ['title', 'category', 'image', 'link', 'sort_order', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $id;
        $sql = "UPDATE hotels SET " . implode(', ', $fields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Hotel updated successfully']);
        } else {
            sendResponse(['error' => 'Hotel not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update hotel: ' . $e->getMessage()], 500);
    }
}

function deleteHotel($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM hotels WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Hotel deleted successfully']);
        } else {
            sendResponse(['error' => 'Hotel not found'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to delete hotel: ' . $e->getMessage()], 500);
    }
}