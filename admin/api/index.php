<?php
/**
 * Apex Luxy Admin Panel API
 * Main API endpoint handler
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../database/config.php';

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$path = str_replace('/admin/api', '', $path);
$segments = explode('/', trim($path, '/'));

$endpoint = $segments[0] ?? '';
$id = $segments[1] ?? null;

// Route requests to appropriate handlers
try {
    switch ($endpoint) {
        case 'sliders':
            require_once 'sliders.php';
            handleSlidersAPI($method, $id);
            break;

        case 'hotels':
            require_once 'hotels.php';
            handleHotelsAPI($method, $id);
            break;

        case 'taxis':
            require_once 'taxis.php';
            handleTaxisAPI($method, $id);
            break;

        case 'pre-wedding':
            require_once 'pre_wedding.php';
            handlePreWeddingAPI($method, $id);
            break;

        case 'about':
            require_once 'about.php';
            handleAboutAPI($method, $id);
            break;

        case 'contact':
            require_once 'contact.php';
            handleContactAPI($method, $id);
            break;

        case 'cta':
            require_once 'cta.php';
            handleCTAAPI($method, $id);
            break;

        case 'reels':
            require_once 'reels.php';
            handleReelsAPI($method, $id);
            break;

        case 'init':
            // Initialize database
            $result = initializeDatabase();
            echo json_encode(['success' => true, 'message' => $result]);
            break;

        case 'test':
            // Test database connection
            $connected = testDBConnection();
            echo json_encode(['success' => $connected, 'message' => $connected ? 'Database connected' : 'Database connection failed']);
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

// Helper function to get JSON input
function getJSONInput() {
    return json_decode(file_get_contents('php://input'), true);
}

// Helper function to send JSON response
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}