<?php
/**
 * Pre-Wedding API Handler
 */

function handlePreWeddingAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getPreWedding($id);
            } else {
                getAllPreWeddings();
            }
            break;

        case 'POST':
            createPreWedding();
            break;

        case 'PUT':
            if ($id) {
                updatePreWedding($id);
            } else {
                sendResponse(['error' => 'ID required for update'], 400);
            }
            break;

        case 'DELETE':
            if ($id) {
                deletePreWedding($id);
            } else {
                sendResponse(['error' => 'ID required for delete'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllPreWeddings() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM pre_wedding WHERE is_active = 1 ORDER BY sort_order ASC");
        $preWeddings = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $preWeddings]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch pre-weddings: ' . $e->getMessage()], 500);
    }
}

function getPreWedding($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM pre_wedding WHERE id = ?");
        $stmt->execute([$id]);
        $preWedding = $stmt->fetch();

        if ($preWedding) {
            sendResponse(['success' => true, 'data' => $preWedding]);
        } else {
            sendResponse(['error' => 'Pre-wedding not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch pre-wedding: ' . $e->getMessage()], 500);
    }
}

function createPreWedding() {
    try {
        $data = getJSONInput();

        if (!$data || !isset($data['title'], $data['category'], $data['image'])) {
            sendResponse(['error' => 'Missing required fields'], 400);
        }

        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO pre_wedding (title, category, image, link, sort_order, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['title'],
            $data['category'],
            $data['image'],
            $data['link'] ?? 'https://wa.me/919653737456',
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? true
        ]);

        $id = $pdo->lastInsertId();
        sendResponse(['success' => true, 'message' => 'Pre-wedding created successfully', 'id' => $id]);

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to create pre-wedding: ' . $e->getMessage()], 500);
    }
}

function updatePreWedding($id) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = ['title', 'category', 'image', 'link', 'sort_order', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $id;
        $sql = "UPDATE pre_wedding SET " . implode(', ', $fields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Pre-wedding updated successfully']);
        } else {
            sendResponse(['error' => 'Pre-wedding not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update pre-wedding: ' . $e->getMessage()], 500);
    }
}

function deletePreWedding($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM pre_wedding WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Pre-wedding deleted successfully']);
        } else {
            sendResponse(['error' => 'Pre-wedding not found'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to delete pre-wedding: ' . $e->getMessage()], 500);
    }
}