<?php
/**
 * Reels API Handler
 */

function handleReelsAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getReel($id);
            } else {
                getAllReels();
            }
            break;

        case 'POST':
            createReel();
            break;

        case 'PUT':
            if ($id) {
                updateReel($id);
            } else {
                sendResponse(['error' => 'ID required for update'], 400);
            }
            break;

        case 'DELETE':
            if ($id) {
                deleteReel($id);
            } else {
                sendResponse(['error' => 'ID required for delete'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllReels() {
    try {
        $pdo = getDBConnection();
        $sectionType = $_GET['section_type'] ?? null;

        if ($sectionType) {
            $stmt = $pdo->prepare("SELECT * FROM reels WHERE section_type = ? AND is_active = 1 ORDER BY sort_order ASC");
            $stmt->execute([$sectionType]);
        } else {
            $stmt = $pdo->query("SELECT * FROM reels WHERE is_active = 1 ORDER BY section_type, sort_order ASC");
        }

        $reels = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $reels]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch reels: ' . $e->getMessage()], 500);
    }
}

function getReel($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM reels WHERE id = ?");
        $stmt->execute([$id]);
        $reel = $stmt->fetch();

        if ($reel) {
            sendResponse(['success' => true, 'data' => $reel]);
        } else {
            sendResponse(['error' => 'Reel not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch reel: ' . $e->getMessage()], 500);
    }
}

function createReel() {
    try {
        $data = getJSONInput();

        if (!$data || !isset($data['section_type'], $data['letter'], $data['title'], $data['category'], $data['image'])) {
            sendResponse(['error' => 'Missing required fields'], 400);
        }

        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO reels (section_type, letter, title, category, image, sort_order, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['section_type'],
            $data['letter'],
            $data['title'],
            $data['category'],
            $data['image'],
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? true
        ]);

        $id = $pdo->lastInsertId();
        sendResponse(['success' => true, 'message' => 'Reel created successfully', 'id' => $id]);

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to create reel: ' . $e->getMessage()], 500);
    }
}

function updateReel($id) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = ['section_type', 'letter', 'title', 'category', 'image', 'sort_order', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $id;
        $sql = "UPDATE reels SET " . implode(', ', $fields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Reel updated successfully']);
        } else {
            sendResponse(['error' => 'Reel not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update reel: ' . $e->getMessage()], 500);
    }
}

function deleteReel($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM reels WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Reel deleted successfully']);
        } else {
            sendResponse(['error' => 'Reel not found'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to delete reel: ' . $e->getMessage()], 500);
    }
}