<?php
/**
 * Sliders API Handler
 */

function handleSlidersAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getSlider($id);
            } else {
                getAllSliders();
            }
            break;

        case 'POST':
            createSlider();
            break;

        case 'PUT':
            if ($id) {
                updateSlider($id);
            } else {
                sendResponse(['error' => 'ID required for update'], 400);
            }
            break;

        case 'DELETE':
            if ($id) {
                deleteSlider($id);
            } else {
                sendResponse(['error' => 'ID required for delete'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllSliders() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM sliders ORDER BY slide_number ASC");
        $sliders = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $sliders]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch sliders: ' . $e->getMessage()], 500);
    }
}

function getSlider($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM sliders WHERE id = ?");
        $stmt->execute([$id]);
        $slider = $stmt->fetch();

        if ($slider) {
            sendResponse(['success' => true, 'data' => $slider]);
        } else {
            sendResponse(['error' => 'Slider not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch slider: ' . $e->getMessage()], 500);
    }
}

function createSlider() {
    try {
        $data = getJSONInput();

        if (!$data || !isset($data['slide_number'], $data['subheading'], $data['heading'], $data['background_image'])) {
            sendResponse(['error' => 'Missing required fields'], 400);
        }

        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO sliders (slide_number, subheading, heading, button_text, button_link, background_image)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['slide_number'],
            $data['subheading'],
            $data['heading'],
            $data['button_text'] ?? 'Know More',
            $data['button_link'] ?? 'https://wa.me/919653737456',
            $data['background_image']
        ]);

        $id = $pdo->lastInsertId();
        sendResponse(['success' => true, 'message' => 'Slider created successfully', 'id' => $id]);

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to create slider: ' . $e->getMessage()], 500);
    }
}

function updateSlider($id) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = ['slide_number', 'subheading', 'heading', 'button_text', 'button_link', 'background_image'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $id;
        $sql = "UPDATE sliders SET " . implode(', ', $fields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Slider updated successfully']);
        } else {
            sendResponse(['error' => 'Slider not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update slider: ' . $e->getMessage()], 500);
    }
}

function deleteSlider($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM sliders WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Slider deleted successfully']);
        } else {
            sendResponse(['error' => 'Slider not found'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to delete slider: ' . $e->getMessage()], 500);
    }
}