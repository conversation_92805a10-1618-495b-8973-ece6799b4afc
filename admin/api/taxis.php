<?php
/**
 * Taxis API Handler
 */

function handleTaxisAPI($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getTaxi($id);
            } else {
                getAllTaxis();
            }
            break;

        case 'POST':
            createTaxi();
            break;

        case 'PUT':
            if ($id) {
                updateTaxi($id);
            } else {
                sendResponse(['error' => 'ID required for update'], 400);
            }
            break;

        case 'DELETE':
            if ($id) {
                deleteTaxi($id);
            } else {
                sendResponse(['error' => 'ID required for delete'], 400);
            }
            break;

        default:
            sendResponse(['error' => 'Method not allowed'], 405);
            break;
    }
}

function getAllTaxis() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT * FROM taxis WHERE is_active = 1 ORDER BY sort_order ASC");
        $taxis = $stmt->fetchAll();
        sendResponse(['success' => true, 'data' => $taxis]);
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch taxis: ' . $e->getMessage()], 500);
    }
}

function getTaxi($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM taxis WHERE id = ?");
        $stmt->execute([$id]);
        $taxi = $stmt->fetch();

        if ($taxi) {
            sendResponse(['success' => true, 'data' => $taxi]);
        } else {
            sendResponse(['error' => 'Taxi not found'], 404);
        }
    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to fetch taxi: ' . $e->getMessage()], 500);
    }
}

function createTaxi() {
    try {
        $data = getJSONInput();

        if (!$data || !isset($data['title'], $data['category'], $data['image'])) {
            sendResponse(['error' => 'Missing required fields'], 400);
        }

        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO taxis (title, category, image, link, sort_order, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $data['title'],
            $data['category'],
            $data['image'],
            $data['link'] ?? 'https://wa.me/919653737456',
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? true
        ]);

        $id = $pdo->lastInsertId();
        sendResponse(['success' => true, 'message' => 'Taxi created successfully', 'id' => $id]);

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to create taxi: ' . $e->getMessage()], 500);
    }
}

function updateTaxi($id) {
    try {
        $data = getJSONInput();

        if (!$data) {
            sendResponse(['error' => 'No data provided'], 400);
        }

        $pdo = getDBConnection();

        // Build dynamic update query
        $fields = [];
        $values = [];

        $allowedFields = ['title', 'category', 'image', 'link', 'sort_order', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }

        if (empty($fields)) {
            sendResponse(['error' => 'No valid fields to update'], 400);
        }

        $values[] = $id;
        $sql = "UPDATE taxis SET " . implode(', ', $fields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Taxi updated successfully']);
        } else {
            sendResponse(['error' => 'Taxi not found or no changes made'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to update taxi: ' . $e->getMessage()], 500);
    }
}

function deleteTaxi($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM taxis WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            sendResponse(['success' => true, 'message' => 'Taxi deleted successfully']);
        } else {
            sendResponse(['error' => 'Taxi not found'], 404);
        }

    } catch (Exception $e) {
        sendResponse(['error' => 'Failed to delete taxi: ' . $e->getMessage()], 500);
    }
}