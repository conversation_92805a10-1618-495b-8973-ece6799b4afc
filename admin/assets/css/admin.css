/* Apex Luxy Admin Panel Styles */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 280px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f4f6f9;
    color: #333;
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-header h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.sidebar-header .text-muted {
    color: rgba(255,255,255,0.7) !important;
    font-size: 0.9rem;
}

.sidebar .nav {
    padding: 1rem 0;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-left-color: var(--secondary-color);
}

.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.15);
    color: white;
    border-left-color: var(--secondary-color);
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.sidebar .dropdown-menu {
    background-color: rgba(0,0,0,0.2);
    border: none;
    margin-left: 1rem;
    margin-right: 1rem;
    border-radius: 0.5rem;
}

.sidebar .dropdown-item {
    color: rgba(255,255,255,0.8);
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.sidebar .dropdown-item:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

/* Main Content Styles */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    padding: 0;
    background-color: #f4f6f9;
}

.content-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.content-header h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--primary-color);
}

.header-actions .btn {
    margin-left: 0.5rem;
}

.content-body {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
}

.card-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1.25rem 1.5rem;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

.card-header h5 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.form-control {
    border: 1px solid #e1e5e9;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #229954;
    border-color: #229954;
    transform: translateY(-1px);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
}

/* Item Cards for Content Management */
.item-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.item-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.item-category {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

.item-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-header {
        padding: 1rem;
    }

    .content-body {
        padding: 1rem;
    }

    .stat-card {
        text-align: center;
        flex-direction: column;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}