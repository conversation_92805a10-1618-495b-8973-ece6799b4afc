/**
 * Apex Luxy Admin Panel JavaScript
 */

class AdminPanel {
    constructor() {
        this.apiBase = 'api/';
        this.currentSection = 'dashboard';
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkDatabaseConnection();
        this.loadDashboardStats();
    }

    bindEvents() {
        // Navigation events
        document.querySelectorAll('[data-section]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('[data-section]').dataset.section;
                this.showSection(section);
            });
        });

        // Database initialization
        document.getElementById('init-db-btn').addEventListener('click', () => {
            this.initializeDatabase();
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshCurrentSection();
        });

        // Form submissions
        document.getElementById('about-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAboutSection();
        });

        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveContactDetails();
        });
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(this.apiBase + endpoint, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'API request failed');
            }

            return result;
        } catch (error) {
            console.error('API Error:', error);
            this.showAlert('error', error.message);
            throw error;
        }
    }

    showAlert(type, message) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of content body
        const contentBody = document.querySelector('.content-body');
        contentBody.insertBefore(alert, contentBody.firstChild);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update content sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Update page title
        const titles = {
            dashboard: 'Dashboard',
            sliders: 'Slider Management',
            hotels: 'Hotels Management',
            taxis: 'Taxis Management',
            'pre-wedding': 'Pre-Wedding Management',
            about: 'About Section',
            contact: 'Contact Details'
        };
        document.getElementById('page-title').textContent = titles[sectionName] || sectionName;

        this.currentSection = sectionName;

        // Load section data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(section) {
        switch (section) {
            case 'dashboard':
                this.loadDashboardStats();
                break;
            case 'sliders':
                this.loadSliders();
                break;
            case 'hotels':
                this.loadHotels();
                break;
            case 'taxis':
                this.loadTaxis();
                break;
            case 'pre-wedding':
                this.loadPreWedding();
                break;
            case 'about':
                this.loadAboutSection();
                break;
            case 'contact':
                this.loadContactDetails();
                break;
        }
    }

    async checkDatabaseConnection() {
        try {
            const result = await this.apiCall('test');
            const statusEl = document.getElementById('db-status');

            if (result.success) {
                statusEl.className = 'alert alert-success';
                statusEl.innerHTML = '<i class="fas fa-check-circle"></i> Database connected successfully';
            } else {
                statusEl.className = 'alert alert-danger';
                statusEl.innerHTML = '<i class="fas fa-exclamation-circle"></i> Database connection failed';
            }
        } catch (error) {
            const statusEl = document.getElementById('db-status');
            statusEl.className = 'alert alert-danger';
            statusEl.innerHTML = '<i class="fas fa-exclamation-circle"></i> Database connection failed';
        }
    }

    async initializeDatabase() {
        try {
            const btn = document.getElementById('init-db-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing...';

            const result = await this.apiCall('init');
            this.showAlert('success', result.message);
            this.checkDatabaseConnection();
            this.loadDashboardStats();
        } catch (error) {
            // Error already handled in apiCall
        } finally {
            const btn = document.getElementById('init-db-btn');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-database"></i> Initialize Database';
        }
    }

    async loadDashboardStats() {
        try {
            // Load counts for each section
            const [sliders, hotels, taxis, preWedding] = await Promise.all([
                this.apiCall('sliders'),
                this.apiCall('hotels'),
                this.apiCall('taxis'),
                this.apiCall('pre-wedding')
            ]);

            document.getElementById('slider-count').textContent = sliders.data?.length || 0;
            document.getElementById('hotel-count').textContent = hotels.data?.length || 0;
            document.getElementById('taxi-count').textContent = taxis.data?.length || 0;
            document.getElementById('prewedding-count').textContent = preWedding.data?.length || 0;
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async loadSliders() {
        try {
            const result = await this.apiCall('sliders');
            const container = document.getElementById('sliders-container');

            if (result.data && result.data.length > 0) {
                container.innerHTML = result.data.map(slider => this.createSliderCard(slider)).join('');
            } else {
                container.innerHTML = '<div class="col-12"><p class="text-muted">No sliders found.</p></div>';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    createSliderCard(slider) {
        return `
            <div class="col-md-6 col-lg-4">
                <div class="item-card">
                    <img src="../${slider.background_image}" alt="${slider.heading}" class="item-image" onerror="this.src='https://via.placeholder.com/300x200?text=Image+Not+Found'">
                    <div class="item-title">Slide ${slider.slide_number}: ${slider.heading}</div>
                    <div class="item-category">${slider.subheading}</div>
                    <div class="item-actions">
                        <button class="btn btn-sm btn-primary" onclick="admin.editSlider(${slider.id})">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="admin.deleteSlider(${slider.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async loadHotels() {
        try {
            const result = await this.apiCall('hotels');
            const container = document.getElementById('hotels-container');

            if (result.data && result.data.length > 0) {
                container.innerHTML = result.data.map(hotel => this.createServiceCard(hotel, 'hotel')).join('');
            } else {
                container.innerHTML = '<div class="col-12"><p class="text-muted">No hotels found.</p></div>';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async loadTaxis() {
        try {
            const result = await this.apiCall('taxis');
            const container = document.getElementById('taxis-container');

            if (result.data && result.data.length > 0) {
                container.innerHTML = result.data.map(taxi => this.createServiceCard(taxi, 'taxi')).join('');
            } else {
                container.innerHTML = '<div class="col-12"><p class="text-muted">No taxis found.</p></div>';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async loadPreWedding() {
        try {
            const result = await this.apiCall('pre-wedding');
            const container = document.getElementById('pre-wedding-container');

            if (result.data && result.data.length > 0) {
                container.innerHTML = result.data.map(item => this.createServiceCard(item, 'prewedding')).join('');
            } else {
                container.innerHTML = '<div class="col-12"><p class="text-muted">No pre-wedding services found.</p></div>';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    createServiceCard(item, type) {
        return `
            <div class="col-md-6 col-lg-4">
                <div class="item-card">
                    <img src="../${item.image}" alt="${item.title}" class="item-image" onerror="this.src='https://via.placeholder.com/300x200?text=Image+Not+Found'">
                    <div class="item-title">${item.title}</div>
                    <div class="item-category">${item.category}</div>
                    <div class="item-actions">
                        <button class="btn btn-sm btn-primary" onclick="admin.editService(${item.id}, '${type}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="admin.deleteService(${item.id}, '${type}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async loadAboutSection() {
        try {
            const result = await this.apiCall('about');
            if (result.data) {
                const data = result.data;
                document.getElementById('about-main-heading').value = data.main_heading || '';
                document.getElementById('about-main-description').value = data.main_description || '';
                document.getElementById('about-background-image').value = data.background_image || '';
                document.getElementById('about-service-1').value = data.service_1 || '';
                document.getElementById('about-service-2').value = data.service_2 || '';
                document.getElementById('about-service-3').value = data.service_3 || '';
                document.getElementById('about-service-4').value = data.service_4 || '';
                document.getElementById('about-service-5').value = data.service_5 || '';
                document.getElementById('about-service-6').value = data.service_6 || '';
                document.getElementById('about-detailed-description').value = data.detailed_description || '';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async saveAboutSection() {
        try {
            const data = {
                main_heading: document.getElementById('about-main-heading').value,
                main_description: document.getElementById('about-main-description').value,
                background_image: document.getElementById('about-background-image').value,
                service_1: document.getElementById('about-service-1').value,
                service_2: document.getElementById('about-service-2').value,
                service_3: document.getElementById('about-service-3').value,
                service_4: document.getElementById('about-service-4').value,
                service_5: document.getElementById('about-service-5').value,
                service_6: document.getElementById('about-service-6').value,
                detailed_description: document.getElementById('about-detailed-description').value
            };

            const result = await this.apiCall('about', 'PUT', data);
            this.showAlert('success', result.message);
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async loadContactDetails() {
        try {
            const result = await this.apiCall('contact');
            if (result.data) {
                const data = result.data;
                document.getElementById('contact-phone').value = data.phone || '';
                document.getElementById('contact-email').value = data.email || '';
                document.getElementById('contact-whatsapp').value = data.whatsapp_number || '';
                document.getElementById('contact-company-name').value = data.company_name || '';
                document.getElementById('contact-address').value = data.address || '';
                document.getElementById('contact-company-tagline').value = data.company_tagline || '';
                document.getElementById('contact-facebook').value = data.facebook_url || '';
                document.getElementById('contact-twitter').value = data.twitter_url || '';
                document.getElementById('contact-instagram').value = data.instagram_url || '';
            }
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    async saveContactDetails() {
        try {
            const data = {
                phone: document.getElementById('contact-phone').value,
                email: document.getElementById('contact-email').value,
                whatsapp_number: document.getElementById('contact-whatsapp').value,
                company_name: document.getElementById('contact-company-name').value,
                address: document.getElementById('contact-address').value,
                company_tagline: document.getElementById('contact-company-tagline').value,
                facebook_url: document.getElementById('contact-facebook').value,
                twitter_url: document.getElementById('contact-twitter').value,
                instagram_url: document.getElementById('contact-instagram').value
            };

            const result = await this.apiCall('contact', 'PUT', data);
            this.showAlert('success', result.message);
        } catch (error) {
            // Error already handled in apiCall
        }
    }

    refreshCurrentSection() {
        this.loadSectionData(this.currentSection);
    }

    // Edit and delete functions for sliders
    async editSlider(id) {
        // For now, just show an alert - you can implement a modal later
        this.showAlert('info', 'Slider editing functionality will be implemented in the next phase');
    }

    async deleteSlider(id) {
        if (confirm('Are you sure you want to delete this slider?')) {
            try {
                const result = await this.apiCall(`sliders/${id}`, 'DELETE');
                this.showAlert('success', result.message);
                this.loadSliders();
                this.loadDashboardStats();
            } catch (error) {
                // Error already handled in apiCall
            }
        }
    }

    // Edit and delete functions for services
    async editService(id, type) {
        // For now, just show an alert - you can implement a modal later
        this.showAlert('info', `${type} editing functionality will be implemented in the next phase`);
    }

    async deleteService(id, type) {
        const typeMap = {
            hotel: 'hotels',
            taxi: 'taxis',
            prewedding: 'pre-wedding'
        };

        if (confirm(`Are you sure you want to delete this ${type}?`)) {
            try {
                const result = await this.apiCall(`${typeMap[type]}/${id}`, 'DELETE');
                this.showAlert('success', result.message);

                // Reload the appropriate section
                switch (type) {
                    case 'hotel':
                        this.loadHotels();
                        break;
                    case 'taxi':
                        this.loadTaxis();
                        break;
                    case 'prewedding':
                        this.loadPreWedding();
                        break;
                }

                this.loadDashboardStats();
            } catch (error) {
                // Error already handled in apiCall
            }
        }
    }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.admin = new AdminPanel();
});