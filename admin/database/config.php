<?php
/**
 * Database Configuration for Apex Luxy Admin Panel
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'apexluxy');
define('DB_USER', 'root');
define('DB_PASS', '');

// Create database connection
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

// Test database connection
function testDBConnection() {
    try {
        $pdo = getDBConnection();
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Initialize database with schema if tables don't exist
function initializeDatabase() {
    try {
        $pdo = getDBConnection();

        // Check if tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE 'sliders'");
        if ($stmt->rowCount() == 0) {
            // Tables don't exist, run schema
            $schema = file_get_contents(__DIR__ . '/schema.sql');
            $pdo->exec($schema);
            return "Database initialized successfully!";
        }
        return "Database already initialized.";
    } catch (Exception $e) {
        return "Error initializing database: " . $e->getMessage();
    }
}
?>