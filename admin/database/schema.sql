-- Apex Luxy Admin Panel Database Schema
-- Database: apexluxy

CREATE DATABASE IF NOT EXISTS apexluxy;
USE apexluxy;

-- Table for slider content (6 slides)
CREATE TABLE IF NOT EXISTS sliders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    slide_number INT NOT NULL,
    subheading <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    heading VA<PERSON><PERSON>R(255) NOT NULL,
    button_text VARCHAR(100) DEFAULT 'Know More',
    button_link VARCHAR(500) DEFAULT 'https://wa.me/************',
    background_image VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_slide (slide_number)
);

-- Table for hotels data
CREATE TABLE IF NOT EXISTS hotels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    image VARCHAR(500) NOT NULL,
    link VARCHAR(500) DEFAULT 'https://wa.me/************',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for taxis data
CREATE TABLE IF NOT EXISTS taxis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    image VARCHAR(500) NOT NULL,
    link VARCHAR(500) DEFAULT 'https://wa.me/************',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for pre-wedding data
CREATE TABLE IF NOT EXISTS pre_wedding (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    image VARCHAR(500) NOT NULL,
    link VARCHAR(500) DEFAULT 'https://wa.me/************',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for about section content
CREATE TABLE IF NOT EXISTS about_section (
    id INT AUTO_INCREMENT PRIMARY KEY,
    main_heading TEXT NOT NULL,
    main_description TEXT NOT NULL,
    background_image VARCHAR(500) NOT NULL,
    service_1 VARCHAR(255) DEFAULT 'Luxury Hotel Bookings',
    service_2 VARCHAR(255) DEFAULT 'Premium Transportation',
    service_3 VARCHAR(255) DEFAULT 'Pre-Wedding Photography',
    service_4 VARCHAR(255) DEFAULT 'Event Planning',
    service_5 VARCHAR(255) DEFAULT 'Concierge Services',
    service_6 VARCHAR(255) DEFAULT 'Destination Experiences',
    detailed_description TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for contact details
CREATE TABLE IF NOT EXISTS contact_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    whatsapp_number VARCHAR(20) NOT NULL,
    facebook_url VARCHAR(500),
    twitter_url VARCHAR(500),
    instagram_url VARCHAR(500),
    company_name VARCHAR(255) DEFAULT 'Apex Luxy',
    company_tagline VARCHAR(500) DEFAULT 'Luxury hospitality & lifestyle concierge based in Udaipur, India',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for CTA (Call to Action) section data
CREATE TABLE IF NOT EXISTS cta_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_type ENUM('hotels', 'taxis', 'pre-wedding') NOT NULL,
    heading TEXT NOT NULL,
    background_image VARCHAR(500) NOT NULL,
    counter_1_number VARCHAR(10) NOT NULL,
    counter_1_label VARCHAR(100) NOT NULL,
    counter_2_number VARCHAR(10) NOT NULL,
    counter_2_label VARCHAR(100) NOT NULL,
    counter_3_number VARCHAR(10) NOT NULL,
    counter_3_label VARCHAR(100) NOT NULL,
    counter_4_number VARCHAR(10) NOT NULL,
    counter_4_label VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_section (section_type)
);

-- Table for reels section data
CREATE TABLE IF NOT EXISTS reels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_type ENUM('hotels', 'taxis', 'pre-wedding') NOT NULL,
    letter VARCHAR(1) NOT NULL,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    image VARCHAR(500) NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default slider data
INSERT INTO sliders (slide_number, subheading, heading, background_image) VALUES
(1, 'Hotel Interior', 'Royal King Suite', 'img/slider/slider1.jpg'),
(2, 'Ultimate Retreat', 'Signature Pool Villa', 'img/slider/slider2.jpg'),
(3, 'Luxury Cab Services', 'Elite Chauffeur Ride', 'img/slider/slider3.png'),
(4, 'Curated Experiences', 'Cinematic Pre-Wedding', 'img/slider/slider4.jpg'),
(5, 'Custom Luxury Events', 'Private Event Spaces', 'img/slider/slider5.jpg'),
(6, 'Handpicked for you', 'Curated Luxury Stays', 'img/slider/slider6.jpg');

-- Insert default hotels data
INSERT INTO hotels (title, category, image, sort_order) VALUES
('Royal Heritage Suite', 'Luxury Resort', 'img/services/hotels/hotel1.png', 1),
('The Serenity Villa', 'Private Pool', 'img/services/hotels/hotel2.png', 2),
('Alpine Crest Retreat', 'Mountain Lodge', 'img/services/hotels/hotel3.png', 3),
('Urban Luxe Hotel', 'Boutique Stay', 'img/services/hotels/hotel4.png', 4),
('The Lakeside Charm', 'Deluxe Room', 'img/services/hotels/hotel5.png', 5),
('Regal Palace Stay', 'Heritage Hotel', 'img/services/hotels/hotel6.png', 6);

-- Insert default taxis data
INSERT INTO taxis (title, category, image, sort_order) VALUES
('Executive Sedan', 'Premium Comfort', 'img/services/taxis/taxi1.png', 1),
('Adventure Suv', 'Spacious Ride', 'img/services/taxis/taxi2.png', 2),
('Classic Vintage Car', 'Perfect For Shoots', 'img/services/taxis/taxi3.png', 3),
('Elite Family Ride', 'Ideal For Groups', 'img/services/taxis/taxi4.png', 4),
('Airport Transfer Pro', 'On-Time Pickup', 'img/services/taxis/taxi5.png', 5),
('Chauffeur Luxe', 'Trained Drivers 24/7', 'img/services/taxis/taxi6.jpeg', 6);

-- Insert default pre-wedding data
INSERT INTO pre_wedding (title, category, image, sort_order) VALUES
('Grace in Tradition', 'Bride Shoot', 'img/services/prewedding/pre1.webp', 1),
('Celebrations Unfold', 'Event Shoot', 'img/services/prewedding/pre2.webp', 2),
('Together, Before Forever', 'Prewedding Shoot', 'img/services/prewedding/pre3.webp', 3),
('Little Smiles, Big Moments', 'Baby/Kid Shoot', 'img/services/prewedding/pre4.webp', 4),
('Styled With Soul', 'Model Shoot', 'img/services/prewedding/pre5.webp', 5),
('Visuals That Sell', 'Commercial Shoot', 'img/services/prewedding/pre6.webp', 6);

-- Insert default about section data
INSERT INTO about_section (main_heading, main_description, background_image, detailed_description) VALUES
('We are Apex Luxy – India\'s premier luxury hospitality and lifestyle concierge service. From handpicked accommodations to seamless transportation and unforgettable pre-wedding experiences, we curate excellence in every detail.',
'Luxury hospitality & lifestyle concierge based in Udaipur, India',
'img/about.png',
'Based in the heart of Rajasthan, we understand the art of hospitality and the importance of creating memorable experiences. Our team personally vets every service provider, ensuring that your journey with us exceeds expectations. From the palaces of Udaipur to modern luxury resorts, we bring you the finest India has to offer.');

-- Insert default contact details
INSERT INTO contact_details (phone, email, address, whatsapp_number, facebook_url, twitter_url, instagram_url) VALUES
('+91 96537 37456', '<EMAIL>', 'City Palace Road, Udaipur, Rajasthan 313001', '************', '#', '#', '#');

-- Insert default CTA sections data
INSERT INTO cta_sections (section_type, heading, background_image, counter_1_number, counter_1_label, counter_2_number, counter_2_label, counter_3_number, counter_3_label, counter_4_number, counter_4_label) VALUES
('hotels', 'Where Comfort Meets Class - Every Detail Refined.', 'img/CTA/hotel.png', '24', 'Premium Properties', '100', 'Personally Verified', '4.8', 'Guest Satisfaction', '📍', 'Location-Centric Stays'),
('taxis', 'Not Just a ride - A seamless Journey', 'img/CTA/taxi.png', '150', 'Available Vehicles', '24', 'All-Day Availability', '100', 'Professional Drivers', '🔒', 'Safe & Secure Riders'),
('pre-wedding', 'Beyond Pictures — Capturing Your Story Before \'I Do\'.', 'img/CTA/prewedding.webp', '50', 'Curated Locations', '7', 'Flexible Scheduling', '100', 'Professional Creators', '🌹', 'Personalized Experiences');

-- Insert default reels data
INSERT INTO reels (section_type, letter, title, category, image, sort_order) VALUES
-- Hotels reels
('hotels', 'R', 'Step Into Royal Comfort', 'Royal Hospitality', 'img/reels/hotels/hotel1.png', 1),
('hotels', 'E', 'Where Elegance Resides', 'Modern Luxury', 'img/reels/hotels/hotel2.png', 2),
('hotels', 'W', 'Wake Up Like Royalty', 'Boutique Stays', 'img/reels/hotels/hotel3.png', 3),
('hotels', 'D', 'Every Detail Designed', 'Timeless Interiors', 'img/reels/hotels/hotel4.png', 4),
('hotels', 'H', 'Evenings In Heritage', 'Luxury Resort', 'img/reels/hotels/hotel5.png', 5),
('hotels', 'Y', 'Your Suite Story Begins', 'Deluxe Rooms', 'img/reels/hotels/hotel6.png', 6),
-- Taxis reels
('taxis', 'L', 'Luxury, At your Doorstep', 'Premium Comfort', 'img/reels/taxi/taxi1.png', 1),
('taxis', 'A', 'Arrive In Style', 'Luxury Fleets', 'img/reels/taxi/taxi2.png', 2),
('taxis', 'R', 'Inside The Royal Ride', 'Executive Sedan', 'img/reels/taxi/taxi3.png', 3),
('taxis', 'D', 'Drive With Distinction', 'Intercity Travel', 'img/reels/taxi/taxi4.png', 4),
('taxis', 'S', 'Smooth, Silent, Sophisticated', 'Special Events', 'img/reels/taxi/taxi5.png', 5),
('taxis', 'J', 'Your Journey, Elevated', 'Chauffeur Luxe', 'img/reels/taxi/taxi6.png', 6),
-- Pre-wedding reels
('pre-wedding', 'L', 'Moments Before Forever', 'Timeless Beginnings', 'img/reels/prewedding/pre1.png', 1),
('pre-wedding', 'O', 'Love In Heritage', 'Vintage Romance Vibes', 'img/reels/prewedding/pre2.png', 2),
('pre-wedding', 'V', 'From Lens To Legacy', 'Captured Legacies', 'img/reels/prewedding/pre3.png', 3),
('pre-wedding', 'E', 'Styled For The Story', 'Aesthetic Storytelling', 'img/reels/prewedding/pre4.png', 4),
('pre-wedding', 'R', 'Golden Hour, Golden Vows', 'Sunset Promises', 'img/reels/prewedding/pre5.png', 5),
('pre-wedding', 'S', 'Cinematic, Cultural, Captivating', 'The Grand Frame', 'img/reels/prewedding/pre6.png', 6);