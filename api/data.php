<?php
/**
 * Main Website Data API
 * Provides data for the main website from the database
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/database/config.php';

// Get the requested data type
$type = $_GET['type'] ?? '';

try {
    $pdo = getDBConnection();

    switch ($type) {
        case 'sliders':
            $stmt = $pdo->query("SELECT * FROM sliders ORDER BY slide_number ASC");
            $data = $stmt->fetchAll();
            break;

        case 'hotels':
            $stmt = $pdo->query("SELECT * FROM hotels WHERE is_active = 1 ORDER BY sort_order ASC");
            $data = $stmt->fetchAll();
            break;

        case 'taxis':
            $stmt = $pdo->query("SELECT * FROM taxis WHERE is_active = 1 ORDER BY sort_order ASC");
            $data = $stmt->fetchAll();
            break;

        case 'pre-wedding':
            $stmt = $pdo->query("SELECT * FROM pre_wedding WHERE is_active = 1 ORDER BY sort_order ASC");
            $data = $stmt->fetchAll();
            break;

        case 'about':
            $stmt = $pdo->query("SELECT * FROM about_section LIMIT 1");
            $data = $stmt->fetch();
            break;

        case 'contact':
            $stmt = $pdo->query("SELECT * FROM contact_details LIMIT 1");
            $data = $stmt->fetch();
            break;

        case 'cta':
            $sectionType = $_GET['section'] ?? '';
            if ($sectionType) {
                $stmt = $pdo->prepare("SELECT * FROM cta_sections WHERE section_type = ?");
                $stmt->execute([$sectionType]);
                $data = $stmt->fetch();
            } else {
                $stmt = $pdo->query("SELECT * FROM cta_sections ORDER BY section_type");
                $data = $stmt->fetchAll();
            }
            break;

        case 'reels':
            $sectionType = $_GET['section'] ?? '';
            if ($sectionType) {
                $stmt = $pdo->prepare("SELECT * FROM reels WHERE section_type = ? AND is_active = 1 ORDER BY sort_order ASC");
                $stmt->execute([$sectionType]);
                $data = $stmt->fetchAll();
            } else {
                $stmt = $pdo->query("SELECT * FROM reels WHERE is_active = 1 ORDER BY section_type, sort_order ASC");
                $data = $stmt->fetchAll();
            }
            break;

        case 'all':
            // Return all data for the website
            $sliders = $pdo->query("SELECT * FROM sliders ORDER BY slide_number ASC")->fetchAll();
            $hotels = $pdo->query("SELECT * FROM hotels WHERE is_active = 1 ORDER BY sort_order ASC")->fetchAll();
            $taxis = $pdo->query("SELECT * FROM taxis WHERE is_active = 1 ORDER BY sort_order ASC")->fetchAll();
            $preWedding = $pdo->query("SELECT * FROM pre_wedding WHERE is_active = 1 ORDER BY sort_order ASC")->fetchAll();
            $about = $pdo->query("SELECT * FROM about_section LIMIT 1")->fetch();
            $contact = $pdo->query("SELECT * FROM contact_details LIMIT 1")->fetch();
            $cta = $pdo->query("SELECT * FROM cta_sections ORDER BY section_type")->fetchAll();
            $reels = $pdo->query("SELECT * FROM reels WHERE is_active = 1 ORDER BY section_type, sort_order ASC")->fetchAll();

            $data = [
                'sliders' => $sliders,
                'hotels' => $hotels,
                'taxis' => $taxis,
                'pre_wedding' => $preWedding,
                'about' => $about,
                'contact' => $contact,
                'cta' => $cta,
                'reels' => $reels
            ];
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid data type requested']);
            exit;
    }

    echo json_encode(['success' => true, 'data' => $data]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
?>